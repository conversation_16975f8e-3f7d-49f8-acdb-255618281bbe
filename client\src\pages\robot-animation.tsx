import React, { useEffect, useRef } from 'react';
import { useLocation } from 'wouter';
import { ArrowLeft, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

const RobotAnimation: React.FC = () => {
  const [, setLocation] = useLocation();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Handle navigation back to home
  const handleGoHome = () => {
    setLocation('/');
  };

  // Handle going back
  const handleGoBack = () => {
    window.history.back();
  };

  useEffect(() => {
    // Set page title
    document.title = 'Daswos Robot Animation';
    
    // Cleanup on unmount
    return () => {
      document.title = 'Daswos';
    };
  }, []);

  return (
    <div className="min-h-screen bg-[#E0E0E0] dark:bg-[#222222] flex flex-col">
      {/* Header with navigation */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Button
            onClick={handleGoBack}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleGoHome}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Home
          </Button>
        </div>
        <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Daswos Robot Animation
        </h1>
        <div className="w-32"></div> {/* Spacer for centering */}
      </div>

      {/* Robot Animation Content */}
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="w-full max-w-6xl bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          {/* Robot Animation Iframe */}
          <iframe
            ref={iframeRef}
            src="/robot-animation/index.html"
            title="Daswos Robot Animation"
            className="w-full h-[80vh] border-0"
            style={{ minHeight: '600px' }}
            onError={() => {
              console.error('Failed to load robot animation');
            }}
          />
        </div>
        
        {/* Instructions */}
        <div className="mt-4 text-center text-gray-600 dark:text-gray-400 max-w-2xl">
          <p className="text-sm">
            Interact with the Daswos robot! Click on the robot to make it move, 
            use the control buttons to change animations, and adjust the robot size with the slider.
          </p>
        </div>
      </div>
    </div>
  );
};

export default RobotAnimation;
