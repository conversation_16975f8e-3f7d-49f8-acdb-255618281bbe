<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daswos Robot Animation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f0f0;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            display: block;
        }
        .controls {
            position: absolute;
            bottom: 20px;
            display: flex;
            gap: 10px;
            z-index: 100;
        }
        .scale-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 100;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .scale-controls label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .scale-slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .scale-slider {
            width: 150px;
            height: 5px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }
        .scale-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4285f4;
            cursor: pointer;
        }
        .scale-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4285f4;
            cursor: pointer;
            border: none;
        }
        .scale-buttons {
            display: flex;
            gap: 5px;
        }
        .scale-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            background-color: #4285f4;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 12px;
        }
        .scale-btn:hover {
            background-color: #3367d6;
        }
        .scale-value {
            font-size: 12px;
            color: #666;
            min-width: 40px;
            text-align: center;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background-color: #4285f4;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #3367d6;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            color: #4285f4;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">Loading Daswos Robot...</div>
    <div class="scale-controls">
        <label>Robot Size</label>
        <div class="scale-slider-container">
            <input type="range" id="scaleSlider" class="scale-slider" min="0.2" max="1.5" step="0.1" value="0.5">
            <span id="scaleValue" class="scale-value">50%</span>
        </div>
        <div class="scale-buttons">
            <button id="scaleDownBtn" class="scale-btn">−</button>
            <button id="resetScaleBtn" class="scale-btn">Reset</button>
            <button id="scaleUpBtn" class="scale-btn">+</button>
        </div>
    </div>
    <div class="controls">
        <button id="idleBtn">Idle</button>
        <button id="talkBtn">Talk</button>
        <button id="danceBtn">Dance</button>
        <button id="rollBtn">Roll</button>
        <button id="searchBtn">Search</button>
        <button id="centerBtn">Center</button>
    </div>
    <script src="robot.js"></script>
</body>
</html>
